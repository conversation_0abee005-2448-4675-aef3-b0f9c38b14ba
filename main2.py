import pypandoc

# 转换 Markdown 为 PDF (使用 weasyprint 引擎，不需要 LaTeX)
try:
    output = pypandoc.convert_file('1.md', 'pdf', outputfile='output.pdf',
                                 extra_args=['--pdf-engine=weasyprint'])
    print("PDF generated successfully using weasyprint!")
except RuntimeError as e:
    print(f"Error with weasyprint: {e}")
    print("Trying with wkhtmltopdf...")
    try:
        output = pypandoc.convert_file('1.md', 'pdf', outputfile='output.pdf',
                                     extra_args=['--pdf-engine=wkhtmltopdf'])
        print("PDF generated successfully using wkhtmltopdf!")
    except RuntimeError as e2:
        print(f"Error with wkhtmltopdf: {e2}")
        print("Please install either weasyprint or wkhtmltopdf:")
        print("pip install weasyprint")
        print("or download wkhtmltopdf from: https://wkhtmltopdf.org/downloads.html")