import pypandoc
import os
from pathlib import Path

# 设置自定义 pandoc 路径
# 获取当前脚本所在目录
current_dir = Path(__file__).parent
pandoc_path = current_dir / "bin" / "pandoc.exe"

# 设置环境变量指向项目下的 pandoc.exe
os.environ['PYPANDOC_PANDOC'] = str(pandoc_path)

# 验证 pandoc 路径是否正确
print(f"使用的 pandoc 路径: {pypandoc.get_pandoc_path()}")
print(f"pandoc 版本: {pypandoc.get_pandoc_version()}")

try:
    # 转换 Markdown 为 PDF
    output = pypandoc.convert_file('1.md', 'pdf', outputfile='output.pdf')
    print("PDF 转换成功！")
except Exception as e:
    print(f"转换失败: {e}")
    print("尝试使用不同的 PDF 引擎...")

    # 如果默认转换失败，尝试使用 weasyprint 引擎
    try:
        output = pypandoc.convert_file('1.md', 'pdf', outputfile='output.pdf',
                                     extra_args=['--pdf-engine=weasyprint'])
        print("PDF 转换成功（使用 weasyprint 引擎）！")
    except Exception as e2:
        print(f"weasyprint 引擎转换失败: {e2}")

        # 尝试使用 wkhtmltopdf 引擎
        try:
            output = pypandoc.convert_file('1.md', 'pdf', outputfile='output.pdf',
                                         extra_args=['--pdf-engine=wkhtmltopdf'])
            print("PDF 转换成功（使用 wkhtmltopdf 引擎）！")
        except Exception as e3:
            print(f"所有转换方法都失败了: {e3}")
            print("请确保安装了相应的 PDF 引擎（如 weasyprint 或 wkhtmltopdf）")
