import markdown
from bs4 import BeautifulSoup
import re
from typing import TypedDict, List, Union, Literal

# 定义返回数据结构的类型
class ContentItem(TypedDict):
    """直接内容项"""
    type: Literal['content']
    text: str

class SubtopicItem(TypedDict):
    """二级段落项"""
    type: Literal['subtopic']
    title: str
    content: str
    details: List[str]

class Topic(TypedDict):
    """会议议题中的主题"""
    title: str
    items: List[Union[ContentItem, SubtopicItem]]

class Agenda(TypedDict):
    """会议议题"""
    topics: List[Topic]

class TodoTable(TypedDict):
    """待办事项表格"""
    headers: List[str]
    rows: List[List[str]]

class MarkdownStructure(TypedDict):
    """Markdown文档结构"""
    title: str
    overview: str
    agenda: Agenda
    conclusion: str
    todo_table: TodoTable

# 原始Markdown文本
markdown_text = """
# AI场控系统功能介绍与配置流程会议

## 会议概述
本次会议主要围绕AI场控系统的功能模块、使用场景、优化更新以及后续的配置流程进行了详细介绍。同时，针对当前直播带货中重复性工作带来的人力成本问题，提出了通过AI工具提升效率的解决方案，并对系统在实际应用中的表现和未来发展方向进行了讨论。

## 会议议题

### 1. AI场控系统核心功能介绍
- **弹窗配置模式**：支持评估模式、假跌单模式、预热模式三种。
  - 评估模式支持单品/多品弹窗循环展示，可实时响应主播语音指令（如"二号链接"）。
  - 假跌单模式在B单过程中不关闭商品链接，仍可下单。
  - 预热模式在关架时进行库存播报预热，但禁止购买。
- **主播离席语音包**：主播临时离开时，可播放预设语音（2-5分钟），回场后可通过快捷键切换。
- **人机交互语音识别**：
  - 支持模糊匹配关键词（如"还有多少单库存"、"还剩几单"等）。
  - 可配置三层语音包随机播放，增强真实感。
- **违禁词消音功能**：自动过滤诱导互动类违规话术，降低限流或关停风险。

### 2. 系统优化与新增功能
- 正则表达式优化：支持模糊匹配规则（如用"叉"代替任意字）。
- 语音识别优化：提升后鼻音、口音、平翘舌识别准确率。
- 新增违禁词消音功能：有效过滤99%以上的违规词汇。
- 集合看板上线计划：整合多个数据源，实现运营数据实时查看与一键复盘。

### 3. 使用效果与案例分享
- 已应用于首农、成都四个项目、青岛啤酒、金麦郎、瑞爱、装油、桃李等多个品牌直播间。
- 成都项目实测节省大量人力沟通时间，减少场控更换带来的管理成本。
- 运营可将更多精力投入到内容创意与脚本策划上。

### 4. 配置流程与培训安排
- 配置人员一对一服务：包括配音设置、关键词描述等。
- 后续组织统一培训：帮助主播熟悉关键词触发机制与系统操作。
- 提供PPT、功能视频、录屏演示等资料辅助理解。

### 5. 当前问题反馈与应对策略
- **AI识别误触发问题**：
  - 极少数情况下可能出现关键词误识别。
  - 解决方案：调整关键词表述或引导主播优化话术。
- **关键词重复触发问题**：
  - 主播重复说同一句话可能多次触发。
  - 属于小细节问题，可通过关键词优化解决。

### 6. 公司业务进展与财务规划
- 技术研发已迭代十余版本，形成两个稳定产品线。
- 团队组建完成，技术、销售、配置人员到位。
- 财务方面设定七月份为盈亏平衡月，目标收入九万元。
- 计划招聘高阶技术专家与专职运营推广人员，提升整体服务能力。

## 结论
AI场控系统已在多个直播间成功试运行，显著降低人力与沟通成本。系统功能完善，涵盖弹窗配置、语音识别、违禁词过滤、集合看板等模块，具备良好的扩展性与实用性。下一步将全面启动配置流程，推动各项目落地使用，并持续优化产品体验与服务能力。

## 待办
| 任务描述 | 负责人 | 截止时间 |
|----------|--------|----------|
| 发送PPT、功能视频、录屏演示至群内 | speaker-3 | 已完成 |
| 安排一对一配置人员对接各直播间 | speaker-3、阿德 | 即将开始 |
| 组织统一培训课程 | speaker-3 | 待定 |
| 招聘高阶技术专家与运营推广人员 | 公司管理层 | 待推进 |
| 实现集合看板与一键复盘功能 | 技术团队 | 下月上线 |
| 监控七月份收入情况，确保达成盈亏平衡目标 | 财务团队 | 7月底前 |
| 优化关键词识别逻辑，减少误触发 | 技术团队 | 持续进行 |
"""

def comprehensive_markdown_extractor(md_text: str) -> MarkdownStructure:
    """
    从Markdown文本中提取会议文档的完整结构
    
    参数:
        md_text: Markdown格式文本
        
    返回:
        包含完整文档结构的字典
    """
    # 按行分割文本
    lines = md_text.strip().split('\n')
    
    result: MarkdownStructure = {
        'title': '',  # 文档标题 (# 级别)
        'overview': '',  # 会议概述
        'agenda': {  # 会议议题
            'topics': []  # 一级段落列表
        },
        'conclusion': '',  # 结论
        'todo_table': {  # 待办表格
            'headers': [],
            'rows': []
        }
    }
    
    current_section = None
    current_topic = None
    current_subtopic = None
    table_mode = False
    in_agenda_content = False
    
    for line in lines:
        # 保留原始行，不要立即strip
        original_line = line
        stripped_line = line.strip()
        
        if not stripped_line:
            continue
            
        # 匹配一级标题 (文档标题)
        if stripped_line.startswith('# '):
            result['title'] = stripped_line[2:].strip()
            
        # 匹配二级标题
        elif stripped_line.startswith('## '):
            section_title = stripped_line[3:].strip()
            table_mode = False
            in_agenda_content = False
            
            if section_title == '会议概述':
                current_section = 'overview'
            elif section_title == '会议议题':
                current_section = 'agenda'
                in_agenda_content = True
            elif section_title == '结论':
                current_section = 'conclusion'
            elif section_title == '待办':
                current_section = 'todo'
                table_mode = True
            else:
                current_section = None
                
        # 匹配三级标题 (会议议题下的一级段落)
        elif stripped_line.startswith('### ') and current_section == 'agenda':
            topic_title = stripped_line[4:].strip()
            current_topic = {
                'title': topic_title,
                'items': []  # 改为items，包含所有子项（二级段落和直接内容）
            }
            result['agenda']['topics'].append(current_topic)
            current_subtopic = None
            
        # 处理表格
        elif table_mode and current_section == 'todo':
            if stripped_line.startswith('|') and stripped_line.endswith('|'):
                cells = [cell.strip() for cell in stripped_line[1:-1].split('|')]
                if not result['todo_table']['headers']:
                    result['todo_table']['headers'] = cells
                elif not all(cell == '' or '-' in cell for cell in cells):
                    # 跳过分隔行
                    result['todo_table']['rows'].append(cells)
                    
        # 处理其他内容
        elif current_section:
            if current_section == 'overview':
                if result['overview']:
                    result['overview'] += '\n' + stripped_line
                else:
                    result['overview'] = stripped_line
                    
            elif current_section == 'conclusion':
                if result['conclusion']:
                    result['conclusion'] += '\n' + stripped_line
                else:
                    result['conclusion'] = stripped_line
                    
            elif current_section == 'agenda' and current_topic:
                # 检查是否是二级段落 (以 - **标题**: 开头)
                if stripped_line.startswith('- **') and '**：' in stripped_line:
                    match = re.match(r'- \*\*(.*?)\*\*：(.*)', stripped_line)
                    if match:
                        subtopic_title = match.group(1).strip()
                        subtopic_content = match.group(2).strip()
                        current_subtopic = {
                            'type': 'subtopic',
                            'title': subtopic_title,
                            'content': subtopic_content,
                            'details': []  # 三级列表
                        }
                        current_topic['items'].append(current_subtopic)
                        
                # 检查是否是三级列表 (以 两个空格 + - 开头) - 现在使用原始行
                elif original_line.startswith('  - '):
                    detail = original_line[4:].strip()
                    if current_subtopic:
                        current_subtopic['details'].append(detail)
                    # 如果没有当前子主题，则忽略（这种情况在正确格式中不应该出现）
                        
                # 检查是否是一级段落下的直接列表项 (以 - 开头，但不是二级段落)
                elif stripped_line.startswith('- ') and not stripped_line.startswith('- **'):
                    content = stripped_line[2:].strip()
                    current_topic['items'].append({
                        'type': 'content',
                        'text': content
                    })
                    # 不重置current_subtopic，因为后续可能还有三级列表归属到上一个subtopic
                    
    return result

def print_extracted_structure(structure: MarkdownStructure):
    """
    打印提取的文档结构
    """
    print("=" * 60)
    print("文档标题:", structure['title'])
    print("=" * 60)
    
    print("\n📋 会议概述:")
    print(structure['overview'])
    
    print("\n📝 会议议题:")
    for i, topic in enumerate(structure['agenda']['topics'], 1):
        print(f"\n  {topic['title']}")
        
        # 按顺序打印所有items
        for item in topic['items']:
            if item['type'] == 'content':
                print(f"    • {item['text']}")
            elif item['type'] == 'subtopic':
                print(f"    ▶ {item['title']}: {item['content']}")
                
                # 打印三级列表
                for detail in item['details']:
                    print(f"      - {detail}")
    
    print(f"\n📊 结论:")
    print(structure['conclusion'])
    
    print(f"\n📋 待办事项:")
    if structure['todo_table']['headers']:
        # 打印表头
        headers = structure['todo_table']['headers']
        print(f"  {' | '.join(headers)}")
        print(f"  {' | '.join(['---'] * len(headers))}")
        
        # 打印表格内容
        for row in structure['todo_table']['rows']:
            print(f"  {' | '.join(row)}")

if __name__ == "__main__":
    # 使用新函数提取内容
    extracted_structure = comprehensive_markdown_extractor(markdown_text)

    # 打印提取结果
    print_extracted_structure(extracted_structure)