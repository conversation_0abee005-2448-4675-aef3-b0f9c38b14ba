import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_pdf import PdfPages
import re

def markdown_to_pdf_matplotlib(md_file, pdf_file):
    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        md_text = f.read()
    
    # 解析 Markdown
    lines = md_text.split('\n')
    
    with PdfPages(pdf_file) as pdf:
        fig, ax = plt.subplots(figsize=(8.5, 11))  # Letter size
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        y_position = 0.95
        line_height = 0.03
        
        for line in lines:
            line = line.strip()
            if not line:
                y_position -= line_height * 0.5
                continue
            
            # 检查是否需要新页面
            if y_position < 0.05:
                pdf.savefig(fig, bbox_inches='tight')
                plt.close(fig)
                fig, ax = plt.subplots(figsize=(8.5, 11))
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.axis('off')
                y_position = 0.95
            
            # 处理标题
            if line.startswith('# '):
                ax.text(0.05, y_position, line[2:], fontsize=18, fontweight='bold', 
                       transform=ax.transAxes)
                y_position -= line_height * 1.5
            elif line.startswith('## '):
                ax.text(0.05, y_position, line[3:], fontsize=14, fontweight='bold', 
                       transform=ax.transAxes)
                y_position -= line_height * 1.2
            elif line.startswith('### '):
                ax.text(0.05, y_position, line[4:], fontsize=12, fontweight='bold', 
                       transform=ax.transAxes)
                y_position -= line_height * 1.1
            # 处理列表
            elif line.startswith('- ') or line.startswith('* '):
                ax.text(0.07, y_position, f"• {line[2:]}", fontsize=10, 
                       transform=ax.transAxes)
                y_position -= line_height
            elif re.match(r'^\d+\. ', line):
                ax.text(0.07, y_position, line, fontsize=10, 
                       transform=ax.transAxes)
                y_position -= line_height
            # 处理普通段落
            else:
                # 简单处理长行
                max_chars = 80
                if len(line) > max_chars:
                    words = line.split(' ')
                    current_line = ''
                    for word in words:
                        if len(current_line + ' ' + word) <= max_chars:
                            current_line = current_line + ' ' + word if current_line else word
                        else:
                            if current_line:
                                ax.text(0.05, y_position, current_line, fontsize=10, 
                                       transform=ax.transAxes)
                                y_position -= line_height
                            current_line = word
                    
                    if current_line:
                        ax.text(0.05, y_position, current_line, fontsize=10, 
                               transform=ax.transAxes)
                        y_position -= line_height
                else:
                    ax.text(0.05, y_position, line, fontsize=10, 
                           transform=ax.transAxes)
                    y_position -= line_height
                
                y_position -= line_height * 0.3  # 段落间距
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)
    
    print(f"PDF generated successfully: {pdf_file}")

if __name__ == "__main__":
    try:
        markdown_to_pdf_matplotlib('1.md', 'output_matplotlib.pdf')
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure matplotlib is installed: pip install matplotlib")
