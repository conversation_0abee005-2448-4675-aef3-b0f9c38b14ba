import subprocess

def markdown_to_word_pandoc(input_md, output_docx):
    """
    使用pandoc将Markdown转换为Word
    需要先安装pandoc: https://pandoc.org/installing.html
    """
    try:
        subprocess.run(["bin/pandoc.exe", input_md, "-o", output_docx], check=True)
        print(f"成功将 {input_md} 转换为 {output_docx}")
    except subprocess.CalledProcessError as e:
        print(f"转换失败: {e}")
    except FileNotFoundError:
        print("未找到pandoc，请先安装pandoc")

# 使用示例
markdown_to_word_pandoc("1.md", "output.pdf")