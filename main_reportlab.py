import markdown
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from bs4 import BeautifulSoup
import re

def markdown_to_pdf_reportlab(md_file, pdf_file):
    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        md_text = f.read()
    
    # 将 Markdown 转换为 HTML
    html_text = markdown.markdown(md_text)
    
    # 解析 HTML
    soup = BeautifulSoup(html_text, 'html.parser')
    
    # 创建 PDF 文档
    doc = SimpleDocTemplate(pdf_file, pagesize=letter)
    styles = getSampleStyleSheet()
    story = []
    
    # 定义样式
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=12,
    )
    
    normal_style = ParagraphStyle(
        'CustomNormal',
        parent=styles['Normal'],
        fontSize=12,
        spaceAfter=12,
    )
    
    # 处理 HTML 元素
    for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'li']):
        text = element.get_text().strip()
        if not text:
            continue
            
        if element.name == 'h1':
            story.append(Paragraph(text, title_style))
        elif element.name in ['h2', 'h3', 'h4', 'h5', 'h6']:
            story.append(Paragraph(text, heading_style))
        elif element.name == 'p':
            story.append(Paragraph(text, normal_style))
        elif element.name in ['ul', 'ol']:
            # 处理列表
            for li in element.find_all('li'):
                li_text = li.get_text().strip()
                if li_text:
                    story.append(Paragraph(f"• {li_text}", normal_style))
        
        story.append(Spacer(1, 6))
    
    # 生成 PDF
    doc.build(story)
    print(f"PDF generated successfully: {pdf_file}")

if __name__ == "__main__":
    try:
        markdown_to_pdf_reportlab('1.md', 'output_reportlab.pdf')
    except ImportError as e:
        print(f"Missing dependency: {e}")
        print("Please install required packages:")
        print("pip install reportlab beautifulsoup4")
    except Exception as e:
        print(f"Error: {e}")
