import pypandoc
import os
from pathlib import Path

def setup_custom_pandoc():
    """设置使用项目下的 pandoc.exe"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    pandoc_path = current_dir / "bin" / "pandoc.exe"
    
    # 检查 pandoc.exe 是否存在
    if not pandoc_path.exists():
        raise FileNotFoundError(f"未找到 pandoc.exe: {pandoc_path}")
    
    # 设置环境变量指向项目下的 pandoc.exe
    os.environ['PYPANDOC_PANDOC'] = str(pandoc_path)
    
    print(f"✓ 使用的 pandoc 路径: {pypandoc.get_pandoc_path()}")
    print(f"✓ pandoc 版本: {pypandoc.get_pandoc_version()}")

def convert_md_to_pdf(input_file, output_file, pdf_engine=None):
    """
    使用项目下的 pandoc.exe 将 Markdown 转换为 PDF
    
    Args:
        input_file: 输入的 Markdown 文件路径
        output_file: 输出的 PDF 文件路径
        pdf_engine: PDF 引擎 ('weasyprint', 'wkhtmltopdf', 'pdflatex' 等)
    """
    try:
        # 设置自定义 pandoc 路径
        setup_custom_pandoc()
        
        # 准备转换参数
        extra_args = []
        if pdf_engine:
            extra_args.append(f'--pdf-engine={pdf_engine}')
        
        # 执行转换
        output = pypandoc.convert_file(input_file, 'pdf', 
                                     outputfile=output_file,
                                     extra_args=extra_args)
        
        engine_info = f" (使用 {pdf_engine} 引擎)" if pdf_engine else ""
        print(f"✓ PDF 转换成功{engine_info}: {output_file}")
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        return False

def convert_with_fallback(input_file, output_file):
    """
    使用多种 PDF 引擎尝试转换，直到成功为止
    """
    # 尝试的 PDF 引擎列表（按优先级排序）
    engines = [
        None,  # 默认引擎
        'weasyprint',
        'wkhtmltopdf',
        'pdflatex'
    ]
    
    for engine in engines:
        print(f"\n尝试转换 {input_file} -> {output_file}")
        if engine:
            print(f"使用 PDF 引擎: {engine}")
        else:
            print("使用默认 PDF 引擎")
            
        if convert_md_to_pdf(input_file, output_file, engine):
            return True
    
    print("\n✗ 所有转换方法都失败了")
    print("请确保安装了相应的 PDF 引擎：")
    print("  - pip install weasyprint")
    print("  - 或下载 wkhtmltopdf: https://wkhtmltopdf.org/downloads.html")
    print("  - 或安装 LaTeX 发行版")
    return False

if __name__ == "__main__":
    # 使用示例
    input_file = '1.md'
    output_file = 'output_custom.pdf'
    
    # 方法1: 直接转换（使用默认引擎）
    # convert_md_to_pdf(input_file, output_file)
    
    # 方法2: 指定特定引擎
    # convert_md_to_pdf(input_file, output_file, 'weasyprint')
    
    # 方法3: 自动尝试多种引擎（推荐）
    convert_with_fallback(input_file, output_file)
